/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
    domains: ['lh3.googleusercontent.com', 'maps.googleapis.com'],
  },
  async rewrites() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap',
      },
    ]
  },
  async headers() {
    return [
      // Global security headers for all routes
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },

      // CF Caching - Static assets (CSS, JS, images)
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'CF-Cache-Control',
            value: 'public, max-age: 31536000, immutable'
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age: 31536000, immutable'
          },
        ],
      },

      // CF Caching - Public folder assets
      {
        source: '/:path*.(png|jpg|jpeg|gif|svg|ico|webp|avif)',
        headers: [
          {
            key: 'CF-Cache-Control',
            value: 'public, max-age: 86400, stale-while-revalidate: 604800'
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age: 86400'
          },
        ],
      },

      // CF Caching - Blog pages (high cache with SWR)
      {
        source: '/blog/:slug*',
        headers: [
          {
            key: 'CF-Cache-Control',
            value: 'public, max-age: 600, stale-while-revalidate: 86400'
          },
          {
            key: 'Cache-Tag',
            value: 'blog'
          },
        ],
      },

      // CF Caching - API routes (short cache for dynamic data)
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'CF-Cache-Control',
            value: 'public, max-age: 300, stale-while-revalidate: 3600'
          },
          {
            key: 'Cache-Tag',
            value: 'api'
          },
        ],
      },

      // CF Caching - Homepage and static pages (medium cache)
      {
        source: '/(about|contact|privacy|terms)',
        headers: [
          {
            key: 'CF-Cache-Control',
            value: 'public, max-age: 1800, stale-while-revalidate: 43200'
          },
        ],
      },
    ]
  },
}

export default nextConfig
