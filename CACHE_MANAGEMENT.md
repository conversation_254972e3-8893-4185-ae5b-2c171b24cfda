# CloudFlare Cache Management Guide

## 🚀 Performance Optimizations Implemented

### Cache Headers Added:
- **Static Assets** (JS/CSS): 1 year cache + immutable
- **Images**: 24 hour cache + 7 day stale-while-revalidate
- **Blog Pages**: 10 minutes + 24 hour stale-while-revalidate
- **API Routes**: 5 minutes + 1 hour stale-while-revalidate
- **Static Pages**: 30 minutes + 12 hour stale-while-revalidate

### Expected Performance Gains:
- **Blog pages: 50-70% faster** (cached HTML + database queries)
- **Assets: 80-90% faster** (edge cached resources)
- **Repeat visitors: 95% faster** (browser + CF cache)

## 🧹 Cache Invalidation Methods

### Method 1: CloudFlare Dashboard
1. Go to your CF Dashboard → Caching → Custom Purge
2. Purge by URL or tag:
   - Specific blog: `https://preview.findcaraccidentlawyers.org/blog/your-post-slug`
   - All blogs: Purge tag `blog`
   - API responses: Purge tag `api`

### Method 2: API Purge (Recommended)
```bash
curl -X POST "https://api.cloudflare.com/client/v4/zones/YOUR_ZONE_ID/purge_cache" \
     -H "Authorization: Bearer YOUR_API_TOKEN" \
     -H "Content-Type: application/json" \
     --data '{
       "tags": ["blog", "api"]
     }'
```

### Method 3: URL-Based Purge
```bash
curl -X POST "https://api.cloudflare.com/client/v4/zones/YOUR_ZONE_ID/purge_cache" \
     -H "Authorization: Bearer YOUR_API_TOKEN" \
     -H "Content-Type: application/json" \
     --data '{
       "files": [
         "https://preview.findcaraccidentlawyers.org/blog",
         "https://preview.findcaraccidentlawyers.org/blog/*"
       ]
     }'
```

## 📊 Performance Testing

### Test Commands:
```bash
# Test blog page load time
curl -H "Accept: text/html" -w "@curl-format.txt" \
     -s -o /dev/null \
     "https://preview.findcaraccidentlawyers.org/blog/9-documents-you-need-for-your-car-accident-case"

# Check cache headers
curl -I "https://preview.findcaraccidentlawyers.org/blog/9-documents-you-need-for-your-car-accident-case"
```

### Expected Headers:
```
CF-Cache-Status: HIT (or MISS on first load)
CF-Cache-Control: public, max-age: 600, stale-while-revalidate: 86400
Cache-Tag: blog
```

## 🔧 Configuration Details

### Cache Durations:
- `max-age`: How long CF serves cached content
- `stale-while-revalidate`: CF refreshes cache in background after max-age
- `immutable`: Tells CF the content never changes (for static assets)

### Cache Tags:
- `blog`: All blog pages
- `api`: API responses
- Use these for targeted invalidation

## ⚠️ Important Notes

1. **Stale-While-Revalidate**: Pages may show outdated content for up to the SWR time
2. **Cache Bypass**: Add `?nocache=1` to URLs to bypass cache for testing
3. **Development**: Caching only applies to production builds via CF
4. **Invalidation Speed**: Purges take 5-10 seconds globally

## 🚨 Troubleshooting

### Slow still after changes?
- Check CF dashboard for cache HIT vs MISS status
- Verify headers are being sent correctly
- Clear browser cache and test again

### Content not updating?
- Use cache purge or wait for SWR time
- Add query param to force reload: `?t=$timestamp`

### Headers not applied?
- Check CF console deployment logs
- Verify headers are added to correct routes
- Test with `curl -I` to inspect headers
