import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, User, Clock } from "lucide-react"
import { Blog } from "@/lib/supabase"
import Link from "next/link"
import Image from "next/image"

interface BlogCardProps {
  blog: Blog
}

export function BlogCard({ blog }: BlogCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const estimateReadTime = (content: string) => {
    const wordsPerMinute = 200
    const wordCount = content.split(' ').length
    return Math.ceil(wordCount / wordsPerMinute)
  }

  return (
    <Card className="h-full hover:shadow-lg transition-shadow duration-200">
      <Link href={`/blog/${blog.slug}`} className="block">
        {blog.featured_image && (
          <div className="relative aspect-[4/3] w-full overflow-hidden rounded-t-lg">
            <Image
              src={blog.featured_image}
              alt={blog.title}
              fill
              className="object-cover transition-transform duration-200 hover:scale-105"
            />
          </div>
        )}

        <CardHeader className="pb-3 pt-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(blog.published_date)}</span>
            <span>•</span>
            <Clock className="h-4 w-4" />
            <span>{estimateReadTime(blog.content)} min read</span>
          </div>
          
          <h3 className="text-xl font-semibold line-clamp-2 hover:text-primary transition-colors">
            {blog.title}
          </h3>
        </CardHeader>
        
        <CardContent className="pt-0">
          {blog.excerpt && (
            <p className="text-muted-foreground line-clamp-3 mb-4 text-base">
              {blog.excerpt}
            </p>
          )}
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <User className="h-4 w-4" />
              <span>{blog.author}</span>
            </div>
            
            <div className="flex flex-wrap gap-1">
              {blog.tags.slice(0, 2).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag.replace('-', ' ')}
                </Badge>
              ))}
              {blog.tags.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{blog.tags.length - 2}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Link>
    </Card>
  )
}
