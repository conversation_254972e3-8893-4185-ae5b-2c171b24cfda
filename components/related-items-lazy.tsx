'use client'

import dynamic from 'next/dynamic'
import { Suspense, useEffect, useState } from 'react'

// Dynamic imports for lazy loading
const RelatedItems = dynamic(() => import('./related-items').then(mod => ({ default: mod.RelatedItems })),
  { ssr: false }
)

interface RelatedItemsLazyProps {
  title: string
  type: 'blogs' | 'attorneys'
  blogId?: string
  attorneyId?: string
  city?: string
  limit?: number
}

// Component that loads related data lazily
function RelatedLazyLoader({
  title,
  type,
  blogId,
  attorneyId,
  city,
  limit
}: RelatedItemsLazyProps) {
  const [relatedData, setRelatedData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadRelatedData = async () => {
      try {
        if (type === 'blogs' && blogId) {
          const { getRelatedBlogs } = await import('../lib/supabase')
          const { blogs } = await getRelatedBlogs(blogId, [], limit || 3)
          setRelatedData(blogs)
        } else if (type === 'attorneys' && attorneyId && city) {
          const { getRelatedAttorneys } = await import('../lib/supabase')
          const { attorneys } = await getRelatedAttorneys(attorneyId, city, limit || 3)
          setRelatedData(attorneys)
        }
      } catch (error) {
        console.error('Error loading related data:', error)
        setRelatedData([])
      } finally {
        setLoading(false)
      }
    }

    loadRelatedData()
  }, [type, blogId, attorneyId, city, limit])

  if (loading) {
    return (
      <section className="mt-12">
        <div className="h-8 w-64 bg-muted rounded animate-pulse mb-6" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: limit }).map((_, i) => (
            <div key={i} className="space-y-4 p-4 bg-card rounded-lg border">
              <div className="h-48 bg-muted rounded animate-pulse" />
              <div className="space-y-2">
                <div className="h-6 bg-muted rounded animate-pulse w-3/4" />
                <div className="flex justify-between">
                  <div className="h-4 bg-muted rounded animate-pulse w-20" />
                  <div className="h-4 bg-muted rounded animate-pulse w-16" />
                </div>
                <div className="flex gap-2">
                  <div className="h-6 bg-muted rounded-full animate-pulse w-16" />
                  <div className="h-6 bg-muted rounded-full animate-pulse w-20" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>
    )
  }

  if (relatedData.length === 0) {
    return null
  }

  return (
    <RelatedItems
      title={title}
      items={relatedData}
      type={type}
    />
  )
}

// Main lazy component
export function RelatedItemsLazy(props: RelatedItemsLazyProps) {
  return <RelatedLazyLoader {...props} />
}
