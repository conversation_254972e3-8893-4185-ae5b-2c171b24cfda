import React from 'react'
import { Skeleton } from './ui/skeleton'

interface ContentSkeletonProps {
  variant?: 'content' | 'header' | 'card'
  lines?: number
}

export function ContentSkeleton({ variant = 'content', lines = 5 }: ContentSkeletonProps) {
  if (variant === 'header') {
    return (
      <div className="space-y-4">
        <Skeleton className="h-12 w-3/4" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-2/3" />
        <div className="flex gap-2">
          {Array.from({ length: 3 }).map((_, i) => (
            <Skeleton key={i} className="h-8 w-20" />
          ))}
        </div>
      </div>
    )
  }

  if (variant === 'card') {
    return (
      <div className="space-y-4 p-4">
        <Skeleton className="h-48 w-full" />
        <Skeleton className="h-6 w-3/4" />
        <div className="flex justify-between">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-16" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-6 w-16" />
          <Skeleton className="h-6 w-20" />
        </div>
      </div>
    )
  }

  // Default content variant
  return (
    <div className="space-y-4">
      {Array.from({ length: lines }).map((_, i) => (
        <div key={i} className="space-y-2">
          <Skeleton className={`h-4 ${i === 0 ? 'w-full' : i === 1 ? 'w-5/6' : i === 2 ? 'w-4/5' : i === 3 ? 'w-3/4' : 'w-4/6'}`} />
          {i === 2 && <Skeleton className="h-4 w-1/2" />}
        </div>
      ))}
    </div>
  )
}

// Specific skeleton for blog articles
export function BlogArticleSkeleton() {
  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header Skeleton */}
      <div className="mb-8">
        <Skeleton className="h-12 w-4/5 md:h-16" />
        <div className="mt-4 space-y-2">
          <Skeleton className="h-6 w-full" />
          <Skeleton className="h-6 w-3/5" />
        </div>

        {/* Meta info skeleton */}
        <div className="flex flex-wrap items-center gap-6 mt-6">
          <div className="flex gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-5 w-24" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-5 w-28" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-5 w-20" />
          </div>
        </div>

        {/* Tags skeleton */}
        <div className="flex gap-2 mt-4">
          <Skeleton className="h-8 w-20 rounded-full" />
          <Skeleton className="h-8 w-24 rounded-full" />
        </div>
      </div>

      {/* Featured image skeleton */}
      <Skeleton className="aspect-[4/3] w-full max-h-80 rounded-lg" />

      {/* Article content skeleton */}
      <ContentSkeleton lines={8} />

      {/* Related items skeleton will be a separate component */}
    </div>
  )
}

// Skeleton for related items section
export function RelatedItemsSkeleton() {
  return (
    <section className="mt-12">
      <Skeleton className="h-8 w-64 mb-6" />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <ContentSkeleton key={i} variant="card" />
        ))}
      </div>
    </section>
  )
}
