// Internal linking utility for SEO optimization
// This utility automatically adds internal links to relevant keywords in blog content

import { getAllBlogsForLinking, Blog } from "@/lib/supabase"

interface InternalLink {
  keyword: string
  url: string
  title: string
}

// Define static internal links for attorney-related terms that should link to main pages
// NOTE: Static links removed to prevent HTML corruption conflicts with dynamic linking
const STATIC_INTERNAL_LINKS: InternalLink[] = []

// Define specific blog-to-blog links for common topics (removed all to prevent corruption)
const SPECIFIC_BLOG_LINKS: InternalLink[] = []

// EXPANDED SAFE KEYWORDS - Based on golden SEO keywords from blog analysis
const BLOG_KEYWORDS = [
  // "personal injury", // Removed to avoid corruption conflicts
  "legal advice",
  "insurance claims",
  "lawsuit",
  "evidence",
  "medical records",
  "pain and suffering",
  "damages",
  "negligence",
  "attorney fees",
  // "car accident", // Temporarily disabled to prevent dynamic linking conflicts
  "settlement",
  "settlements",
  "accident scene",
  "fault determination",
  "driver duties",
  "legal obligations",
  "court proceedings",
  "insurance adjuster",
  "claim process",
  "compensation calculation",
  "compensation",
  "accident procedures",
  "immediate actions",
  "safety",
  "legal process",
  "documentation",
  "lawsuits",
  "legal costs",
  "contingency fees"
]

// Cache for blog posts to avoid repeated API calls
let blogPostsCache: Blog[] | null = null
let cacheTimestamp: number = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

/**
 * Get all blog posts for linking, with caching
 */
async function getBlogPostsForLinking(): Promise<Blog[]> {
  const now = Date.now()

  // Return cached data if it's still fresh
  if (blogPostsCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return blogPostsCache
  }

  try {
    const { blogs } = await getAllBlogsForLinking()
    blogPostsCache = blogs
    cacheTimestamp = now
    return blogs
  } catch (error) {
    console.error("Error fetching blogs for linking:", error)
    return blogPostsCache || []
  }
}

/**
 * Find the most relevant blog post for a given keyword
 */
function findRelevantBlogPost(keyword: string, blogs: Blog[], currentSlug?: string): InternalLink | null {
  const keywordLower = keyword.toLowerCase()

  // Filter out the current blog post
  const availableBlogs = blogs.filter(blog => blog.slug !== currentSlug)

  if (availableBlogs.length === 0) return null

  // Score blogs based on keyword relevance
  const scoredBlogs = availableBlogs.map(blog => {
    let score = 0
    const titleLower = blog.title.toLowerCase()
    const contentLower = blog.content.toLowerCase()
    const tagsLower = blog.tags.map(tag => tag.toLowerCase())
    const slugLower = blog.slug.toLowerCase()

    // Highest score for exact title matches
    if (titleLower === keywordLower) score += 20

    // High score for title containing keyword
    if (titleLower.includes(keywordLower)) score += 15

    // High score for slug matches (URL-friendly versions)
    if (slugLower.includes(keywordLower.replace(/\s+/g, '-'))) score += 12

    // Medium-high score for tag matches
    if (tagsLower.some(tag => tag === keywordLower)) score += 10
    if (tagsLower.some(tag => tag.includes(keywordLower) || keywordLower.includes(tag))) score += 7

    // Medium score for excerpt matches (if available)
    if (blog.excerpt && blog.excerpt.toLowerCase().includes(keywordLower)) score += 6

    // Lower score for content matches, but prioritize early mentions
    const contentMatches = (contentLower.match(new RegExp(keywordLower, 'g')) || []).length
    const firstMatchIndex = contentLower.indexOf(keywordLower)
    if (firstMatchIndex !== -1) {
      // Bonus for early mentions in content
      if (firstMatchIndex < 500) score += 4
      score += Math.min(contentMatches, 3) // Cap at 3 points for content matches
    }

    return { blog, score }
  })

  // Sort by score and get the best match
  scoredBlogs.sort((a, b) => b.score - a.score)
  const bestMatch = scoredBlogs[0]

  if (bestMatch && bestMatch.score > 0) {
    return {
      keyword,
      url: `/blog/${bestMatch.blog.slug}`,
      title: bestMatch.blog.title
    }
  }

  return null
}

/**
 * Process HTML content more carefully to avoid breaking existing tags
 * @param htmlContent - HTML content to process
 * @param currentSlug - Current blog post slug
 * @returns Processed HTML content
 */
async function processHtmlContent(htmlContent: string, currentSlug?: string): Promise<string> {
  // For now, return the content as-is to avoid breaking HTML
  // This is a simplified approach - in a production system, you'd want to use
  // a proper HTML parser to safely add links without breaking existing markup
  return htmlContent
}

/**
 * Adds internal links to blog content for SEO purposes
 * @param content - The blog content to process
 * @param currentSlug - The current blog post slug to avoid self-linking
 * @returns Content with internal links added
 */
export async function addInternalLinks(content: string, currentSlug?: string): Promise<string> {
  let processedContent = content
  const usedLinks = new Set<string>() // Track which keywords we've already linked

  // Skip processing if this is a markdown header (starts with #)
  if (content.trim().startsWith('#')) {
    return content
  }

  // Skip processing if content already contains markdown links
  if (content.includes('[') && content.includes('](')) {
    return content
  }

  // Get all blog posts for dynamic linking
  const blogs = await getBlogPostsForLinking()

  // Combine static links, specific blog links, and dynamic blog links
  const allLinks: InternalLink[] = [...STATIC_INTERNAL_LINKS, ...SPECIFIC_BLOG_LINKS]

  // Add dynamic blog links for blog keywords
  for (const keyword of BLOG_KEYWORDS) {
    const blogLink = findRelevantBlogPost(keyword, blogs, currentSlug)
    if (blogLink) {
      allLinks.push(blogLink)
    }
  }

  // Sort links by keyword length (longest first) to avoid partial matches
  const sortedLinks = allLinks.sort((a, b) => b.keyword.length - a.keyword.length)

  for (const link of sortedLinks) {
    // Skip if we've already used this keyword
    if (usedLinks.has(link.keyword.toLowerCase())) {
      continue
    }

    // Create case-insensitive regex that matches whole words only
    const regex = new RegExp(`\\b(${link.keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})\\b`, 'gi')

    // Check if the keyword exists in the content
    const matches = processedContent.match(regex)
    if (matches && matches.length > 0) {
      // Only link the first occurrence to avoid over-optimization
      let linkAdded = false
      processedContent = processedContent.replace(regex, (match) => {
        if (!linkAdded) {
          linkAdded = true
          usedLinks.add(link.keyword.toLowerCase())
          return `<a href="${link.url}" title="${link.title}" class="text-primary hover:underline font-medium">${match}</a>`
        }
        return match
      })
    }
  }

  return processedContent
}

/**
 * Processes blog content paragraphs and adds internal links
 * @param paragraphs - Array of content paragraphs
 * @param currentSlug - Current blog post slug
 * @returns Processed paragraphs with internal links
 */
export async function processContentWithLinks(paragraphs: string[], currentSlug?: string): Promise<string[]> {
  const fullContent = paragraphs.join('\n')
  const processedContent = await addInternalLinks(fullContent, currentSlug)
  return processedContent.split('\n')
}

/**
 * Safely renders HTML content with internal links
 * @param htmlContent - HTML content string
 * @returns JSX element with dangerously set innerHTML
 */
export function renderContentWithLinks(htmlContent: string) {
  return {
    __html: htmlContent
  }
}
