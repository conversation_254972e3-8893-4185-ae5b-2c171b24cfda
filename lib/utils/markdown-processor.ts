import { remark } from 'remark'
import remarkGfm from 'remark-gfm'
import remarkHtml from 'remark-html'

/**
 * Process markdown content on the server side
 * This function converts markdown to HTML
 */
export async function processMarkdownToHtml(markdownContent: string, currentSlug?: string): Promise<string> {
  try {
    // Add internal links to MARKDOWN content FIRST (before HTML conversion)
    let linkedMarkdown = markdownContent
    if (currentSlug) {
      const { addInternalLinks } = await import('./internal-links')
      linkedMarkdown = await addInternalLinks(markdownContent, currentSlug)
    }

    // THEN convert the linked markdown to HTML
    const processedContent = await remark()
      .use(remarkGfm)
      .use(remarkHtml, { sanitize: false })
      .process(linkedMarkdown)

    return processedContent.toString()
  } catch (error) {
    console.error('Error processing markdown:', error)
    // Fallback: return the original content with basic processing
    return markdownContent.replace(/\n/g, '<br>')
  }
}

/**
 * Add internal links to HTML content (safer than adding to markdown)
 */
async function addInternalLinksToHtml(htmlContent: string, currentSlug: string): Promise<string> {
  try {
    // Import the internal links function
    const { addInternalLinks } = await import('./internal-links')

    // Use a proper HTML parser approach to safely add links
    // For now, disable internal linking as the current implementation is breaking HTML
    // TODO: Implement proper HTML parsing with a library like cheerio
    console.warn('Internal linking disabled in HTML content to prevent markup corruption')

    return htmlContent
  } catch (error) {
    console.error('Error adding internal links:', error)
    return htmlContent
  }
}

/**
 * Extract plain text from markdown for meta descriptions and excerpts
 */
export function extractPlainTextFromMarkdown(markdownContent: string): string {
  return markdownContent
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold formatting
    .replace(/\*(.*?)\*/g, '$1') // Remove italic formatting
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links, keep text
    .replace(/`(.*?)`/g, '$1') // Remove inline code formatting
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()
}

/**
 * Estimate reading time from markdown content
 */
export function estimateReadingTime(markdownContent: string): number {
  const plainText = extractPlainTextFromMarkdown(markdownContent)
  const wordsPerMinute = 200
  const wordCount = plainText.split(' ').filter(word => word.length > 0).length
  return Math.ceil(wordCount / wordsPerMinute)
}
