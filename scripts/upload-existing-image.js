#!/usr/bin/env node

/**
 * Upload Existing Image for Blog Post
 * Downloads an existing image and uploads it to R2, then updates the database
 */

const { createClient } = require('@supabase/supabase-js');
const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
const envPath = path.join(__dirname, '..', '.env.local');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  for (const line of envLines) {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const [key, ...valueParts] = trimmedLine.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=');
        process.env[key] = value;
      }
    }
  }
}

// Configuration
const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL,
  key: process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  tableName: 'findcaraccidentattorneys-blog'
};

const R2_CONFIG = {
  endpoint: process.env.R2_ENDPOINT,
  bucket: process.env.R2_BUCKET_NAME,
  customDomain: process.env.R2_PUBLIC_URL_BASE,
  accessKeyId: process.env.R2_ACCESS_KEY_ID,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  region: process.env.R2_REGION
};

// Initialize clients
let supabase = null;
let s3 = null;

if (SUPABASE_CONFIG.key) {
  supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);
}

if (R2_CONFIG.accessKeyId && R2_CONFIG.secretAccessKey) {
  s3 = new AWS.S3({
    endpoint: R2_CONFIG.endpoint,
    accessKeyId: R2_CONFIG.accessKeyId,
    secretAccessKey: R2_CONFIG.secretAccessKey,
    region: R2_CONFIG.region,
    signatureVersion: 'v4',
  });
}

/**
 * Download image from URL
 */
async function downloadImage(imageUrl) {
  console.log(`⬇️ Downloading image from: ${imageUrl}`);
  const response = await fetch(imageUrl);
  if (!response.ok) {
    throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
  }
  return response.arrayBuffer();
}

/**
 * Upload image to Cloudflare R2
 */
async function uploadToR2(imageBuffer, slug) {
  const key = `blog-images/${slug}.webp`;

  console.log(`☁️ Uploading to R2: ${key}`);

  const uploadParams = {
    Bucket: R2_CONFIG.bucket,
    Key: key,
    Body: imageBuffer,
    ContentType: 'image/webp',
    CacheControl: 'public, max-age=31536000', // 1 year cache
  };

  await s3.upload(uploadParams).promise();
  const finalUrl = `${R2_CONFIG.customDomain}/${key}`;
  console.log(`✅ Uploaded to R2: ${finalUrl}`);
  return finalUrl;
}

/**
 * Update blog post with featured image URL
 */
async function updateBlogPost(blogId, imageUrl) {
  console.log(`💾 Updating database for blog: ${blogId}`);

  const { error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .update({ featured_image: imageUrl })
    .eq('id', blogId);

  if (error) {
    throw new Error(`Failed to update blog post: ${error.message}`);
  }

  console.log('✅ Database updated successfully');
}

/**
 * Main function
 */
async function main() {
  const blogId = process.argv[2] || '5790bab2-790c-4358-8c5e-4f969334878f';
  const slug = process.argv[3] || 'speed-up-car-accident-settlement-process';
  const imageUrl = process.argv[4] || 'https://replicate.delivery/xezq/wrI0e00fb6uFTUMDANf3owVFYmM4j5eAj3M47XhQo54AapKVB/out-0.webp';

  try {
    console.log('🚀 Starting image upload for existing image...');
    console.log(`📄 Blog ID: ${blogId}`);
    console.log(`🗂️  Slug: ${slug}`);
    console.log(`🖼️  Image URL: ${imageUrl}`);

    // Download image
    console.log('⬇️ Downloading image...');
    const imageArrayBuffer = await downloadImage(imageUrl);
    const imageBuffer = Buffer.from(imageArrayBuffer);

    // Upload to R2
    console.log('☁️ Uploading to R2...');
    const r2Url = await uploadToR2(imageBuffer, slug);

    // Update database
    console.log('💾 Updating database...');
    await updateBlogPost(blogId, r2Url);

    console.log('\n🎉 SUCCESS! Image uploaded and blog updated!');
    console.log('🔗 Public R2 URL:', r2Url);

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
