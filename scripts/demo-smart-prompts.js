#!/usr/bin/env node

/**
 * Demo Smart Image Prompt Generation
 * Shows the difference between accident-related and general legal prompts
 */

/**
 * Detect car accident related keywords in title and content
 */
function detectCarAccidentKeywords(title, content = '') {
  const combinedText = (title + ' ' + content).toLowerCase();

  const accidentKeywords = [
    'car accident', 'car crash', 'car collision', 'auto accident', 'vehicle crash',
    'traffic accident', 'road accident', 'motor vehicle accident', 'collision',
    'car wreck', 'auto crash', 'vehicle collision', 'drunk driving', 'dwi',
    'hit and run', 'pedestrian accident', 'motorcycle accident', 'truck accident',
    'commercial vehicle accident', 'wrongful death', 'personal injury',
    'insurance claim', 'insurance coverage', 'car damage', 'fender bender',
    'rear-end collision', 'head-on collision', 'side-impact collision',
    'rollover accident', 'seatbelt injury', 'airbag deployment'
  ];

  return accidentKeywords.some(keyword => combinedText.includes(keyword));
}

/**
 * Generate enhanced image prompt from blog title and content
 */
function generateImagePrompt(title, content) {
  // Extract first few sentences for context
  const contentSummary = content
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold formatting
    .replace(/\*(.*?)\*/g, '$1') // Remove italic formatting
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .split('.').slice(0, 2).join('.') // First 2 sentences
    .substring(0, 200); // Limit length

  // Check for car accident keywords
  const hasCarAccidentKeywords = detectCarAccidentKeywords(title, content);

  let enhancedContent = contentSummary;

  // If car accident keywords detected, enhance the prompt with specific imagery
  if (hasCarAccidentKeywords && !enhancedContent.includes('car accident')) {
    enhancedContent = `Insurance companies often use deceptive tactics to minimize payouts for accident victims. This creates devastating consequences for families already struggling with car accident trauma and medical bills. ${contentSummary}`.substring(0, 200);
  }

  const prompt = `${title} - ${enhancedContent} - IMPORTANT: Generate a candid, raw mobile phone style photograph without any text, words, or written content visible in the image. ${
    hasCarAccidentKeywords
      ? 'Real-world car accident scene showing damaged vehicles, emergency responders, police investigation, tow trucks, crumpled metal, or legal consultation setting with legal paperwork and attorney client discussions. Professional but authentic documentary style photography of auto accidents.'
      : 'Real-world legal consultation setting with attorney and client discussions, legal paperwork, professional office environments, or insurance company negotiations. Professional but authentic documentary style photography.'
  }`;

  return prompt;
}

// Demo different blog titles
const testTitles = [
  '7 Common Lies Insurance Companies Tell Accident Victims',
  'How to Choose the Best Car Accident Attorney',
  'What Documents Do I Need for Insurance Claims',
  'Different Types of Business Law Practice Areas',
  'Understanding Legal Contracts and Agreements'
];

console.log('🎯 SMART PROMPT GENERATION DEMO\n');
console.log('All images will now be generated at 4:3 aspect ratio!\n');

testTitles.forEach((title, index) => {
  const hasKeywords = detectCarAccidentKeywords(title, '');
  const prompt = generateImagePrompt(title, '');

  console.log(`${index + 1}. "${title}"`);
  console.log(`   🔍 Contains Accident Keywords: ${hasKeywords ? '✅ YES' : '❌ NO'}`);
  console.log(`   🎨 Aspect Ratio: 4:3 (wider format)`);

  if (hasKeywords) {
    console.log(`   🖼️  Image Style: Car accident scenes, emergency responders, damaged vehicles`);
  } else {
    console.log(`   🖼️  Image Style: Legal consultations, offices, paperwork discussions`);
  }

  console.log(`   📝 Enhanced Prompt: ${prompt.substring(prompt.indexOf('IMPORTANT:'), 120)}...`);
  console.log('');
});

console.log('🚀 Your updated script will now:');
console.log('1. Generate ALL images at 4:3 aspect ratio');
console.log('2. Automatically detect car accident keywords');
console.log('3. Enhance prompts with appropriate imagery');
console.log('4. Upload to Cloudflare R2 and update database');
console.log('\nRun: node scripts/generate-blog-images.js');
