#!/usr/bin/env node

/**
 * Generate Featured Images for Blog Posts
 *
 * This script reads blog posts from Supabase, generates featured images using
 * Google's Imagen-4-fast model on Replicate, uploads them to Cloudflare R2, and updates the database.
 */

const { createClient } = require('@supabase/supabase-js');
const AWS = require('aws-sdk');
const Replicate = require('replicate');

// Configuration
const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://rhcwssrsdwakputbbpuf.supabase.co',
  key: process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  tableName: 'findcaraccidentattorneys-blog'
};

const R2_CONFIG = {
  endpoint: 'https://fa488c806af1dafb4525e54efb42f2ea.r2.cloudflarestorage.com',
  bucket: 'directory-findcaraccidentlawyers-live-040925',
  customDomain: 'https://images.findcaraccidentlawyers.org',
  accessKeyId: process.env.R2_ACCESS_KEY_ID,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  region: 'auto'
};

const REPLICATE_CONFIG = {
  token: process.env.REPLICATE_API_TOKEN
};

// Initialize clients (only if credentials are available)
let supabase = null;
let s3 = null;

if (SUPABASE_CONFIG.key) {
  supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);
}

if (R2_CONFIG.accessKeyId && R2_CONFIG.secretAccessKey) {
  s3 = new AWS.S3({
    endpoint: R2_CONFIG.endpoint,
    accessKeyId: R2_CONFIG.accessKeyId,
    secretAccessKey: R2_CONFIG.secretAccessKey,
    region: R2_CONFIG.region,
    signatureVersion: 'v4',
  });
}

/**
 * Detect car accident related keywords in title and content
 */
function detectCarAccidentKeywords(title, content = '') {
  const combinedText = (title + ' ' + content).toLowerCase();

  const accidentKeywords = [
    'car accident', 'car crash', 'car collision', 'auto accident', 'vehicle crash',
    'traffic accident', 'road accident', 'motor vehicle accident', 'collision',
    'car wreck', 'auto crash', 'vehicle collision', 'drunk driving', 'dwi',
    'hit and run', 'pedestrian accident', 'motorcycle accident', 'truck accident',
    'commercial vehicle accident', 'wrongful death', 'personal injury',
    'insurance claim', 'insurance coverage', 'car damage', 'fender bender',
    'rear-end collision', 'head-on collision', 'side-impact collision',
    'rollover accident', 'seatbelt injury', 'airbag deployment'
  ];

  return accidentKeywords.some(keyword => combinedText.includes(keyword));
}

/**
 * Generate enhanced image prompt from blog title and content
 */
function generateImagePrompt(title, content) {
  // Extract first few sentences for context
  const contentSummary = content
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold formatting
    .replace(/\*(.*?)\*/g, '$1') // Remove italic formatting
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .split('.').slice(0, 2).join('.') // First 2 sentences
    .substring(0, 200); // Limit length

  // Check for car accident keywords
  const hasCarAccidentKeywords = detectCarAccidentKeywords(title, content);

  let enhancedContent = contentSummary;

  // If car accident keywords detected, enhance the prompt with specific imagery
  if (hasCarAccidentKeywords && !enhancedContent.includes('car accident')) {
    enhancedContent = `Insurance companies often use deceptive tactics to minimize payouts for accident victims. This creates devastating consequences for families already struggling with car accident trauma and medical bills. ${contentSummary}`.substring(0, 200);
  }

  const prompt = hasCarAccidentKeywords
    ? `Generate a candid, raw mobile phone style photograph of a real-world car accident scene showing damaged vehicles, emergency responders, police investigation, tow trucks, crumpled metal without any text, words, or written content visible in the image. Professional but authentic documentary style. ${title}`
    : `Generate a candid, raw mobile phone style photograph of a legal consultation setting with attorney and client discussions, legal paperwork, professional office environments without any text, words, or written content visible in the image. Professional but authentic documentary style. ${title}`;

  return prompt;
}

/**
 * Generate image using Google Imagen-4-fast model
 */
async function generateImage(prompt) {
  try {
    console.log('🎨 Using Google Imagen-4-fast model to generate image...');
    console.log('📝 Prompt:', prompt);

    // Initialize Replicate client
    const replicate = new Replicate({
      auth: REPLICATE_CONFIG.token,
    });

    // Configure input for Imagen-4-fast
    const input = {
      prompt: prompt,
      aspect_ratio: "16:9", // 16:9 aspect ratio (wider format) for blog images
      // Note: Imagen-4-fast is optimized for speed and automatically chooses best quality
    };

    console.log('🔮 Starting image generation...');

    // Run the model
    const output = await replicate.run("google/imagen-4-fast", { input });

    if (!output) {
      throw new Error('No output received from Imagen-4-fast model');
    }

    console.log('✅ Image generation completed!');
    return Array.isArray(output) ? output[0] : output; // Handle array or single URL response

  } catch (error) {
    console.error('Error generating image:', error);

    // Log more specific error details
    if (error.response) {
      console.error('API Response:', error.response.data);
    }

    throw error;
  }
}

/**
 * Download image from URL
 */
async function downloadImage(imageUrl) {
  console.log(`⬇️ Downloading image from: ${imageUrl}`);
  const response = await fetch(imageUrl);
  if (!response.ok) {
    throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
  }
  return Buffer.from(await response.arrayBuffer());
}

/**
 * Upload image to Cloudflare R2
 */
async function uploadToR2(imageBuffer, slug) {
  const key = `blog-images/${slug}.webp`;
  
  const uploadParams = {
    Bucket: R2_CONFIG.bucket,
    Key: key,
    Body: imageBuffer,
    ContentType: 'image/webp',
    CacheControl: 'public, max-age=31536000', // 1 year cache
  };

  try {
    await s3.upload(uploadParams).promise();
    return `${R2_CONFIG.customDomain}/${key}`;
  } catch (error) {
    console.error('Error uploading to R2:', error);
    throw error;
  }
}

/**
 * Update blog post with featured image URL
 */
async function updateBlogPost(blogId, imageUrl) {
  const { error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .update({ featured_image: imageUrl })
    .eq('id', blogId);

  if (error) {
    throw new Error(`Failed to update blog post: ${error.message}`);
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🚀 Starting blog image generation...');

    // Check required environment variables
    if (!REPLICATE_CONFIG.token) {
      throw new Error('REPLICATE_API_TOKEN environment variable is required');
    }
    if (!R2_CONFIG.accessKeyId || !R2_CONFIG.secretAccessKey) {
      throw new Error('R2_ACCESS_KEY_ID and R2_SECRET_ACCESS_KEY environment variables are required');
    }

    // Fetch blog posts without featured images
    const { data: blogs, error } = await supabase
      .from(SUPABASE_CONFIG.tableName)
      .select('id, title, slug, content, excerpt')
      .is('featured_image', null)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch blog posts: ${error.message}`);
    }

    if (!blogs || blogs.length === 0) {
      console.log('✅ No blog posts found without featured images.');
      return;
    }

    console.log(`📝 Found ${blogs.length} blog posts without featured images.`);

    // Process each blog post
    for (let i = 0; i < blogs.length; i++) {
      const blog = blogs[i];
      console.log(`\n🔄 Processing ${i + 1}/${blogs.length}: "${blog.title}"`);

      try {
        // Generate image prompt
        const prompt = generateImagePrompt(blog.title, blog.content || blog.excerpt || '');
        console.log(`📝 Generated prompt: ${prompt.substring(0, 100)}...`);

        // Generate image
        console.log('🎨 Generating image...');
        const imageUrl = await generateImage(prompt);
        console.log(`✅ Image generated: ${imageUrl}`);

        // Download image
        console.log('⬇️ Downloading image...');
        const imageBuffer = await downloadImage(imageUrl);

        // Upload to R2
        console.log('☁️ Uploading to R2...');
        const r2Url = await uploadToR2(imageBuffer, blog.slug);
        console.log(`✅ Uploaded to R2: ${r2Url}`);

        // Update database
        console.log('💾 Updating database...');
        await updateBlogPost(blog.id, r2Url);
        console.log(`✅ Updated blog post: ${blog.title}`);

        // Add delay to avoid rate limiting
        if (i < blogs.length - 1) {
          console.log('⏳ Waiting 5 seconds before next image...');
          await new Promise(resolve => setTimeout(resolve, 5000));
        }

      } catch (error) {
        console.error(`❌ Error processing "${blog.title}":`, error.message);
        // Continue with next blog post
      }
    }

    console.log('\n🎉 Blog image generation completed!');

  } catch (error) {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
