#!/usr/bin/env node

/**
 * Test Image Generation for Single Blog Post
 *
 * This script tests the new Google Imagen-4-fast model on a single blog post
 * before running the full batch script.
 */

const { createClient } = require('@supabase/supabase-js');
const AWS = require('aws-sdk');
const Replicate = require('replicate');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
const envPath = path.join(__dirname, '..', '.env.local');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  for (const line of envLines) {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const [key, ...valueParts] = trimmedLine.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=');
        process.env[key] = value;
      }
    }
  }
}

// Configuration - use the same as main script
const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL,
  key: process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  tableName: 'findcaraccidentattorneys-blog'
};

const R2_CONFIG = {
  endpoint: process.env.R2_ENDPOINT,
  bucket: process.env.R2_BUCKET_NAME,
  customDomain: process.env.R2_PUBLIC_URL_BASE,
  accessKeyId: process.env.R2_ACCESS_KEY_ID,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  region: process.env.R2_REGION
};

const REPLICATE_CONFIG = {
  token: process.env.REPLICATE_API_TOKEN
};

// Initialize clients
let supabase = null;
let s3 = null;

if (SUPABASE_CONFIG.key) {
  supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);
}

if (R2_CONFIG.accessKeyId && R2_CONFIG.secretAccessKey) {
  s3 = new AWS.S3({
    endpoint: R2_CONFIG.endpoint,
    accessKeyId: R2_CONFIG.accessKeyId,
    secretAccessKey: R2_CONFIG.secretAccessKey,
    region: R2_CONFIG.region,
    signatureVersion: 'v4',
  });
}

/**
 * Detect car accident related keywords in title and content
 */
function detectCarAccidentKeywords(title, content = '') {
  const combinedText = (title + ' ' + content).toLowerCase();

  const accidentKeywords = [
    'car accident', 'car crash', 'car collision', 'auto accident', 'vehicle crash',
    'traffic accident', 'road accident', 'motor vehicle accident', 'collision',
    'car wreck', 'auto crash', 'vehicle collision', 'drunk driving', 'dwi',
    'hit and run', 'pedestrian accident', 'motorcycle accident', 'truck accident',
    'commercial vehicle accident', 'wrongful death', 'personal injury',
    'insurance claim', 'insurance coverage', 'car damage', 'fender bender',
    'rear-end collision', 'head-on collision', 'side-impact collision',
    'rollover accident', 'seatbelt injury', 'airbag deployment'
  ];

  return accidentKeywords.some(keyword => combinedText.includes(keyword));
}

/**
 * Generate enhanced image prompt from blog title and content
 */
function generateImagePrompt(title, content) {
  // Extract first few sentences for context
  const contentSummary = content
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold formatting
    .replace(/\*(.*?)\*/g, '$1') // Remove italic formatting
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .split('.').slice(0, 2).join('.') // First 2 sentences
    .substring(0, 200); // Limit length

  // Check for car accident keywords
  const hasCarAccidentKeywords = detectCarAccidentKeywords(title, content);

  let enhancedContent = contentSummary;

  // If car accident keywords detected, enhance the prompt with specific imagery
  if (hasCarAccidentKeywords && !enhancedContent.includes('car accident')) {
    enhancedContent = `Insurance companies often use deceptive tactics to minimize payouts for accident victims. This creates devastating consequences for families already struggling with car accident trauma and medical bills. ${contentSummary}`.substring(0, 200);
  }

  const prompt = `${title} - ${enhancedContent} - IMPORTANT: Generate a candid, raw mobile phone style photograph without any text, words, or written content visible in the image. ${
    hasCarAccidentKeywords
      ? 'Real-world car accident scene showing damaged vehicles, emergency responders, police investigation, tow trucks, crumpled metal, or legal consultation setting with legal paperwork and attorney client discussions. Professional but authentic documentary style photography of auto accidents.'
      : 'Real-world legal consultation setting with attorney and client discussions, legal paperwork, professional office environments, or insurance company negotiations. Professional but authentic documentary style photography.'
  }`;

  return prompt;
}

/**
 * Generate image using Google Imagen-4-fast model
 */
async function generateImage(prompt) {
  console.log('🎨 Using Google Imagen-4-fast model...');
  console.log('📝 Prompt:', prompt);

  const replicate = new Replicate({
    auth: REPLICATE_CONFIG.token,
  });

  const input = {
    prompt: prompt,
    aspect_ratio: "4/3",
  };

  console.log('🔮 Starting image generation...');
  const output = await replicate.run("google/imagen-4-fast", { input });

  if (!output) {
    throw new Error('No output received from Imagen-4-fast model');
  }

  console.log('✅ Image generation completed!');
  return output.url();
}

/**
 * Download image from URL
 */
async function downloadImage(imageUrl) {
  console.log(`⬇️ Downloading image from: ${imageUrl}`);
  const fetch = (await import('node-fetch')).default;
  const response = await fetch(imageUrl);
  if (!response.ok) {
    throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
  }
  return response.buffer();
}

/**
 * Upload image to Cloudflare R2
 */
async function uploadToR2(imageBuffer, slug) {
  const key = `blog-images/${slug}.webp`;

  console.log(`☁️ Uploading to R2: ${key}`);

  const uploadParams = {
    Bucket: R2_CONFIG.bucket,
    Key: key,
    Body: imageBuffer,
    ContentType: 'image/webp',
    CacheControl: 'public, max-age=31536000', // 1 year cache
  };

  await s3.upload(uploadParams).promise();
  const finalUrl = `${R2_CONFIG.customDomain}/${key}`;
  console.log(`✅ Uploaded to R2: ${finalUrl}`);
  return finalUrl;
}

/**
 * Update blog post with featured image URL
 */
async function updateBlogPost(blogId, imageUrl) {
  console.log(`💾 Updating database for blog: ${blogId}`);

  const { error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .update({ featured_image: imageUrl })
    .eq('id', blogId);

  if (error) {
    throw new Error(`Failed to update blog post: ${error.message}`);
  }

  console.log('✅ Database updated successfully');
}

/**
 * Main test function for single blog post
 */
async function testSingleBlog(slug) {
  try {
    console.log(`🚀 Testing image generation for blog: ${slug}`);

    // Check required environment variables
    if (!REPLICATE_CONFIG.token || REPLICATE_CONFIG.token === 'your_replicate_api_token_here') {
      throw new Error('REPLICATE_API_TOKEN needs to be set in .env.local\nGet your token from: https://replicate.com/account');
    }

    // Get the specific blog post
    console.log('🔍 Fetching blog post from Supabase...');
    const { data: blog, error } = await supabase
      .from(SUPABASE_CONFIG.tableName)
      .select('id, title, slug, content, excerpt')
      .eq('slug', slug)
      .single();

    if (error) {
      throw new Error(`Failed to fetch blog post: ${error.message}`);
    }

    if (!blog) {
      throw new Error(`Blog post with slug "${slug}" not found`);
    }

    console.log('✅ Found blog:', blog.title);

    // Generate image prompt
    const prompt = generateImagePrompt(blog.title, blog.content || blog.excerpt || '');
    console.log(`📝 Generated prompt: ${prompt.substring(0, 100)}...`);

    // Generate image
    console.log('🎨 Generating image...');
    const imageUrl = await generateImage(prompt);
    console.log(`✅ Image generated: ${imageUrl}`);

    // Download image
    console.log('⬇️ Downloading image...');
    const imageBuffer = await downloadImage(imageUrl);

    // Upload to R2
    console.log('☁️ Uploading to R2...');
    const r2Url = await uploadToR2(imageBuffer, blog.slug);
    console.log(`✅ Uploaded to R2: ${r2Url}`);

    // Update database
    console.log('💾 Updating database...');
    await updateBlogPost(blog.id, r2Url);

    console.log('\n🎉 SUCCESS! Image generated and stored for:', blog.title);
    console.log('🔗 Image URL:', r2Url);

    return r2Url;

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Test with the specific blog post
if (require.main === module) {
  const slug = process.argv[2] || '7-common-lies-insurance-companies-tell-accident-victims';
  testSingleBlog(slug);
}

module.exports = { testSingleBlog };
