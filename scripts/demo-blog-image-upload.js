#!/usr/bin/env node

/**
 * Demo: Upload Generated Blog Images to R2 and Update Database
 * 
 * This script demonstrates how to upload the generated blog images to Cloudflare R2
 * and update the database with the custom domain URLs.
 */

const { createClient } = require('@supabase/supabase-js');
const AWS = require('aws-sdk');

// Configuration
const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://rhcwssrsdwakputbbpuf.supabase.co',
  key: process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  tableName: 'findcaraccidentattorneys-blog'
};

const R2_CONFIG = {
  endpoint: 'https://fa488c806af1dafb4525e54efb42f2ea.r2.cloudflarestorage.com',
  bucket: 'directory-findcaraccidentlawyers-live-040925',
  customDomain: 'https://images.findcaraccidentlawyers.org',
  accessKeyId: process.env.R2_ACCESS_KEY_ID,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  region: 'auto'
};

// Demo images generated with MCP tool
const DEMO_IMAGES = [
  {
    slug: 'documents-prepare-car-accident-claim',
    title: 'What Documents Do I Need to Prepare for a Car Accident Claim?',
    imageUrl: 'https://replicate.delivery/xezq/gNEPKVrCevSGf0kCj3WjcVwpeSmmeGnShFjbvrNUso77ofSqC/out-0.webp'
  },
  {
    slug: 'cost-hire-car-accident-lawyer',
    title: 'How Much Does It Cost to Hire a Car Accident Lawyer?',
    imageUrl: 'https://replicate.delivery/xezq/hp5VevFvszy9aqOr6ew7UVSBp9keqWfqMHEVeaXtkXzLTflUF/out-0.webp'
  }
];

// Initialize clients (only if credentials are available)
let supabase = null;
let s3 = null;

if (SUPABASE_CONFIG.key) {
  supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);
}

if (R2_CONFIG.accessKeyId && R2_CONFIG.secretAccessKey) {
  s3 = new AWS.S3({
    endpoint: R2_CONFIG.endpoint,
    accessKeyId: R2_CONFIG.accessKeyId,
    secretAccessKey: R2_CONFIG.secretAccessKey,
    region: R2_CONFIG.region,
    signatureVersion: 'v4',
  });
}

/**
 * Download image from URL
 */
async function downloadImage(imageUrl) {
  console.log(`⬇️ Downloading image from: ${imageUrl}`);
  const fetch = (await import('node-fetch')).default;
  const response = await fetch(imageUrl);
  if (!response.ok) {
    throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
  }
  return response.buffer();
}

/**
 * Upload image to Cloudflare R2
 */
async function uploadToR2(imageBuffer, slug) {
  const key = `blog-images/${slug}.webp`;
  
  console.log(`☁️ Uploading to R2: ${key}`);
  
  const uploadParams = {
    Bucket: R2_CONFIG.bucket,
    Key: key,
    Body: imageBuffer,
    ContentType: 'image/webp',
    CacheControl: 'public, max-age=31536000', // 1 year cache
  };

  try {
    await s3.upload(uploadParams).promise();
    const finalUrl = `${R2_CONFIG.customDomain}/${key}`;
    console.log(`✅ Uploaded successfully: ${finalUrl}`);
    return finalUrl;
  } catch (error) {
    console.error('Error uploading to R2:', error);
    throw error;
  }
}

/**
 * Update blog post with featured image URL
 */
async function updateBlogPost(slug, imageUrl) {
  console.log(`💾 Updating database for slug: ${slug}`);
  
  const { error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .update({ featured_image: imageUrl })
    .eq('slug', slug);

  if (error) {
    throw new Error(`Failed to update blog post: ${error.message}`);
  }
  
  console.log(`✅ Database updated for: ${slug}`);
}

/**
 * Process a single image
 */
async function processImage(imageData) {
  try {
    console.log(`\n🔄 Processing: "${imageData.title}"`);
    
    // Download image
    const imageBuffer = await downloadImage(imageData.imageUrl);
    
    // Upload to R2
    const r2Url = await uploadToR2(imageBuffer, imageData.slug);
    
    // Update database
    await updateBlogPost(imageData.slug, r2Url);
    
    console.log(`✅ Completed: ${imageData.title}`);
    return r2Url;
    
  } catch (error) {
    console.error(`❌ Error processing "${imageData.title}":`, error.message);
    throw error;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🚀 Starting demo blog image upload...');

    // Check required environment variables
    if (!R2_CONFIG.accessKeyId || !R2_CONFIG.secretAccessKey || !SUPABASE_CONFIG.key) {
      console.log('⚠️ Required credentials not found. This is a demo script.');
      console.log('Set the following environment variables to actually upload:');
      console.log('- R2_ACCESS_KEY_ID and R2_SECRET_ACCESS_KEY for Cloudflare R2');
      console.log('- SUPABASE_SERVICE_KEY for database updates');
      console.log('For now, showing what would happen...\n');
      
      // Demo mode - show what would happen
      for (const imageData of DEMO_IMAGES) {
        console.log(`📝 Would process: "${imageData.title}"`);
        console.log(`   Slug: ${imageData.slug}`);
        console.log(`   Source: ${imageData.imageUrl}`);
        console.log(`   Target: ${R2_CONFIG.customDomain}/blog-images/${imageData.slug}.webp`);
        console.log('');
      }
      
      return;
    }

    // Process each demo image
    for (let i = 0; i < DEMO_IMAGES.length; i++) {
      const imageData = DEMO_IMAGES[i];
      
      try {
        await processImage(imageData);
        
        // Add delay between uploads
        if (i < DEMO_IMAGES.length - 1) {
          console.log('⏳ Waiting 2 seconds before next upload...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
      } catch (error) {
        console.error(`❌ Failed to process "${imageData.title}":`, error.message);
        // Continue with next image
      }
    }

    console.log('\n🎉 Demo blog image upload completed!');

  } catch (error) {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, processImage };
