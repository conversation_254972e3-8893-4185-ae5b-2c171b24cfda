# Blog Image Generation with MCP

This document outlines how to generate featured images for blog posts using the Replicate Flux MCP tool.

## Prerequisites

1. Supabase access to the `findcaraccidentattorneys-blog` table
2. Cloudflare R2 credentials for image storage
3. Access to the Replicate Flux MCP tool

## Process

### 1. Query Blog Posts Without Images

First, query the Supabase database to find blog posts without featured images:

```sql
SELECT id, title, slug, content, excerpt 
FROM "findcaraccidentattorneys-blog" 
WHERE featured_image IS NULL 
ORDER BY created_at DESC;
```

### 2. Generate Image for Each Post

For each blog post, use the MCP tool to generate an image:

**MCP Tool Call:**
```javascript
use_mcp_tool('replicate-flux-mcp', 'generate_image', {
  prompt: `${blogTitle} - ${contentSummary} - IMPORTANT: Generate a candid, raw mobile phone style photograph without any text, words, or written content visible in the image. Real-world car accident scene or legal consultation setting. Professional but authentic documentary style photography.`,
  aspect_ratio: '1:1',
  output_format: 'webp',
  output_quality: 80,
  num_inference_steps: 4,
  disable_safety_checker: false
});
```

### 3. Upload to Cloudflare R2

Upload the generated image to R2 with the following configuration:

- **Endpoint:** `https://fa488c806af1dafb4525e54efb42f2ea.r2.cloudflarestorage.com`
- **Bucket:** `directory-findcaraccidentlawyers-live-040925`
- **Path:** `blog-images/{slug}.webp`
- **Custom Domain:** `https://images.findcaraccidentlawyers.org`

### 4. Update Database

Update the blog post with the image URL:

```sql
UPDATE "findcaraccidentattorneys-blog" 
SET featured_image = 'https://images.findcaraccidentlawyers.org/blog-images/{slug}.webp'
WHERE id = '{blog_id}';
```

## Image Generation Guidelines

- **Style:** Candid, raw mobile phone photography
- **Content:** Car accident scenes, legal consultations, professional settings
- **Restrictions:** No text, words, or written content in images
- **Aspect Ratio:** 1:1 (square)
- **Format:** WebP for optimal web performance
- **Quality:** 80% for balance between size and quality

## Example Prompts

1. **Car Accident Guide:** "What to do after a car accident essential steps - A candid mobile phone photograph of a minor car accident scene with two vehicles, emergency responders in background, no text visible, authentic documentary style"

2. **Legal Consultation:** "Is it worth getting attorney for car accident - A raw mobile phone photo of a professional legal consultation meeting, attorney and client discussing documents, office setting, no text or signage visible"

3. **Insurance Claims:** "How much car insurance go up after accident - A candid photograph of someone reviewing insurance documents on their phone after a car accident, realistic mobile photography style, no text visible"

## Automation Script

The `scripts/generate-blog-images.js` file provides a Node.js script that can be run to automate this process. However, for optimal results using the MCP tool, it's recommended to run this process through the AI assistant interface.

## Usage with AI Assistant

To generate images for all blog posts without featured images:

1. Ask the AI assistant to query the database for posts without images
2. For each post, ask the assistant to generate an image using the MCP tool
3. Have the assistant upload the image to R2
4. Update the database with the new image URL

This approach ensures proper use of the MCP tool and maintains consistency with the project's architecture.
