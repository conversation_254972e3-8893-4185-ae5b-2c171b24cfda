#!/usr/bin/env node

/**
 * Upload Blog Images to Cloudflare R2 and Update Database
 * 
 * This script uploads the generated blog images to Cloudflare R2 and updates
 * the database with the custom domain URLs.
 */

const { createClient } = require('@supabase/supabase-js');
const AWS = require('aws-sdk');

// Configuration
const SUPABASE_CONFIG = {
  url: 'https://rhcwssrsdwakputbbpuf.supabase.co',
  key: 'sb_publishable_oIFU2557bCSsMHmplaGSeg_VkTmMr0K',
  tableName: 'findcaraccidentattorneys-blog'
};

const CLOUDFLARE_R2_CONFIG = {
  endpoint: 'https://fa488c806af1dafb4525e54efb42f2ea.r2.cloudflarestorage.com',
  bucketName: 'directory-findcaraccidentlawyers-live-040925',
  region: 'auto',
  accessKeyId: 'c789b94077ced966f3c5491e846fefa3',
  secretAccessKey: '****************************************************************',
  publicUrlBase: 'https://images.findcaraccidentlawyers.org'
};

// Blog posts with their generated image URLs (Updated with fresh images)
const BLOG_IMAGES = [
  // First batch - most important blog posts
  {
    slug: 'documents-prepare-car-accident-claim',
    imageUrl: 'https://replicate.delivery/xezq/IVEvkmSYqg6wOZWgTofCjxSgfOdeL5SjQQjarSZphe2gThJVB/out-0.webp'
  },
  {
    slug: 'cost-hire-car-accident-lawyer',
    imageUrl: 'https://replicate.delivery/xezq/LWFefgRBdhkqskgg3r8lHhXX65rFAfPKbCpfbXHFe0TFnCTqC/out-0.webp'
  },
  {
    slug: 'choose-best-car-accident-attorney',
    imageUrl: 'https://replicate.delivery/xezq/Vfe2BnWM0gsCp0P8o2yza9EVrLSlo0qJ23ileOzF9DbxpwkqA/out-0.webp'
  },
  {
    slug: 'how-long-after-accident-file-lawsuit',
    imageUrl: 'https://replicate.delivery/xezq/eOZnsVKsPtT0YaTVAC1xmSX1IX0KX3eTGbzWhvrFDDy4UYSVA/out-0.webp'
  },
  {
    slug: 'good-car-accident-settlement-figure',
    imageUrl: 'https://replicate.delivery/xezq/T77nPhG3vYYYBx5L3ib2Csh8TWxIpILfizwf5Vqk9qY4UYSVA/out-0.webp'
  },
  // Second batch
  {
    slug: 'stay-vehicle-after-car-accident',
    imageUrl: 'https://replicate.delivery/xezq/TqF5a1oVdZZfV6fmgqXOiRzazqeSXAcAHnfFsIwtBj7SUhJVB/out-0.webp'
  },
  {
    slug: 'can-drive-car-after-accident',
    imageUrl: 'https://replicate.delivery/xezq/Heq0CDOGfwm4pkruGEslo0DIUHr6JMxsRB7wz8HFj8tEVYSVA/out-0.webp'
  },
  {
    slug: 'should-admit-fault-accident-scene',
    imageUrl: 'https://replicate.delivery/xezq/QaefVHP5z5rUUka5T5af7Rm21fXuKl6efSYKnjbAmT0PRFmUF/out-0.webp'
  },
  {
    slug: 'duties-driver-car-accident-scenario',
    imageUrl: 'https://replicate.delivery/xezq/tfZN4tRAJs0e2EOXC6h07hB0GKsA9HxRCdxKH8DLAaQEVYSVA/out-0.webp'
  },
  {
    slug: 'driver-responsibilities-car-accident',
    imageUrl: 'https://replicate.delivery/xezq/NY9sNI7iYyqfLyWuyg4nBOUNv71SKzokj4B8y7oDTKAiKMpKA/out-0.webp'
  },
  // Third batch
  {
    slug: 'first-step-after-car-collision',
    imageUrl: 'https://replicate.delivery/xezq/UZVLeq8Wm4UoXCbu4EPAyFzxstGg5UsHjWT5zEpEiyUnKMpKA/out-0.webp'
  },
  {
    slug: 'filing-insurance-claim-minor-car-accident',
    imageUrl: 'https://replicate.delivery/xezq/Gkgewks6Ea2CYacmlngbEgfm3dnfWp97uCNrBaqudbmdqwkqA/out-0.webp'
  },
  {
    slug: 'what-do-immediately-following-car-accident',
    imageUrl: 'https://replicate.delivery/xezq/Yee2gvEyDKs4skfRMgjKSmgY2pEfHfTmafsQohe8A6JJnKMpKA/out-0.webp'
  },
  {
    slug: '7-essential-things-after-car-collision',
    imageUrl: 'https://replicate.delivery/xezq/hRlkDUUsWbKmP93bMsffhu2GCHQJt3UU8NSLPFCcgFHOVYSVA/out-0.webp'
  },
  {
    slug: '25000-good-settlement-car-accident',
    imageUrl: 'https://replicate.delivery/xezq/kSfF0OWtWhUXK6zsKrIXpKTw95n0MapfvOHYeouws9scqwkqA/out-0.webp'
  },
  // Fourth batch
  {
    slug: 'reasonable-car-accident-settlement-offer',
    imageUrl: 'https://replicate.delivery/xezq/bmrZFPnNayZLB9RTw3g75x7RsC9Gfh5jzryLGCJp21csKMpKA/out-0.webp'
  },
  {
    slug: 'typical-pain-suffering-amount-car-accident',
    imageUrl: 'https://replicate.delivery/xezq/N4G9n08TeAX3f0pEgy26yf0SdGSGlgcDlPvcitPcge7gVhJVB/out-0.webp'
  },
  {
    slug: 'hiring-car-accident-attorney-worth-it',
    imageUrl: 'https://replicate.delivery/xezq/wXnG1dqbAT5sDl8eE303q9RscTJDUfm11ycx4xenPxrxqwkqA/out-0.webp'
  },
  {
    slug: 'can-personal-injury-lawyer-drop-case',
    imageUrl: 'https://replicate.delivery/xezq/A2zI910mnvqYAB0eiLOQuyZwsz8tuJaMfU2YQbXOY7DYVYSVA/out-0.webp'
  },
  {
    slug: 'speed-up-car-accident-settlement-process',
    imageUrl: 'https://replicate.delivery/xezq/F4HiCwfUTj23JaXqije6tffgaMysC96kAG1Kz0mjnMeBrCTqC/out-0.webp'
  },
  // Fifth batch
  {
    slug: 'compensation-being-hit-by-car',
    imageUrl: 'https://replicate.delivery/xezq/6tSpQOXrFU7nEFRecRrEqqT9BhJEsgMCVejP0K5alMkhVYSVA/out-0.webp'
  },
  {
    slug: 'why-personal-injury-attorneys-bad-reputation',
    imageUrl: 'https://replicate.delivery/xezq/TXx2VrtTy4pxDRNQfYeD0HCdSxJjExdXO5qRcjqebsKDrwkqA/out-0.webp'
  },
  {
    slug: 'how-personal-injury-lawyers-paid-if-lose',
    imageUrl: 'https://replicate.delivery/xezq/4clSKlcTlkpMPtK0gbm0Gy2eg9esBdePXTqNIZw9E8fHWhJVB/out-0.webp'
  },
  {
    slug: 'maximize-car-accident-settlement-amount',
    imageUrl: 'https://replicate.delivery/xezq/Q4uInpz98fWLCaVZeryZF8wMvynvLXie9fNIpUFoh8sEWhJVB/out-0.webp'
  },
  {
    slug: 'what-happens-court-after-car-accident-settlement',
    imageUrl: 'https://replicate.delivery/xezq/9PsJ6wIAqBpGFhYyX2DLpccZGVZdmNsPva6g1Wfs7UwwKMpKA/out-0.webp'
  },
  // Final batch
  {
    slug: 'maximum-sue-car-accident-amount',
    imageUrl: 'https://replicate.delivery/xezq/DybYVqfDzyRbBqeMyDc5401lZTO2kuvwUyTAWpKDjqeZrwkqA/out-0.webp'
  },
  {
    slug: 'how-often-personal-injury-lawyers-win-cases',
    imageUrl: 'https://replicate.delivery/xezq/q4bWd3Ywp1bvHh9fvJfs4YAfP1d9bHCvXYdzXMHT7xXYrwkqA/out-0.webp'
  },
  {
    slug: 'how-long-settle-car-accident-claim',
    imageUrl: 'https://replicate.delivery/xezq/fCZOfheVShUNhIlsWjRGQ7uMjeeIF9dB1hCohjdTF7sntCTqC/out-0.webp'
  },
  {
    slug: 'how-hard-win-car-accident-lawsuit',
    imageUrl: 'https://replicate.delivery/xezq/in4eZa56lGyZdijsMz1BRXZYHZUrcxrTyM30I49yzoP2KMpKA/out-0.webp'
  },
  {
    slug: 'average-car-accident-settlement-amount-us',
    imageUrl: 'https://replicate.delivery/xezq/AaSbrOmMhbZQEtbq8ZTSPWKsf8X4orhNGsLyWdFbS0E2KMpKA/out-0.webp'
  }
];

// Initialize clients
const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);

function initializeR2Client() {
  return new AWS.S3({
    endpoint: CLOUDFLARE_R2_CONFIG.endpoint,
    accessKeyId: CLOUDFLARE_R2_CONFIG.accessKeyId,
    secretAccessKey: CLOUDFLARE_R2_CONFIG.secretAccessKey,
    region: CLOUDFLARE_R2_CONFIG.region,
    signatureVersion: 'v4',
    s3ForcePathStyle: true
  });
}

// Upload image to R2 and return custom domain URL
async function uploadImageToR2(imageUrl, filename) {
  console.log(`☁️ Uploading ${filename} to R2...`);
  
  try {
    // Download image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status}`);
    }
    
    const imageBuffer = Buffer.from(await response.arrayBuffer());
    const r2Client = initializeR2Client();
    
    // Upload to R2
    const uploadParams = {
      Bucket: CLOUDFLARE_R2_CONFIG.bucketName,
      Key: filename,
      Body: imageBuffer,
      ContentType: 'image/webp',
      ACL: 'public-read'
    };
    
    await r2Client.upload(uploadParams).promise();
    
    // Return custom domain URL
    const customUrl = `${CLOUDFLARE_R2_CONFIG.publicUrlBase}/${filename}`;
    console.log(`✅ Uploaded successfully: ${customUrl}`);
    
    return customUrl;
    
  } catch (error) {
    console.error(`❌ Upload failed: ${error.message}`);
    return null;
  }
}

// Update blog post with image URL
async function updateBlogPost(slug, imageUrl) {
  console.log(`💾 Updating blog post "${slug}"...`);

  try {
    const { error } = await supabase
      .from(SUPABASE_CONFIG.tableName)
      .update({
        featured_image: imageUrl,
        updated_at: new Date().toISOString()
      })
      .eq('slug', slug);

    if (error) {
      throw error;
    }

    console.log('✅ Blog post updated successfully');
    return true;

  } catch (error) {
    console.error(`❌ Update failed: ${error.message}`);
    return false;
  }
}

// Process all blog posts
async function processAllBlogPosts() {
  console.log('🚀 Starting blog image upload process...\n');
  
  let successCount = 0;
  let failureCount = 0;
  
  for (const blogPost of BLOG_IMAGES) {
    console.log(`\n🔄 Processing: "${blogPost.slug}"`);
    
    // Generate filename
    const filename = `blog-images/${blogPost.slug}.webp`;
    
    // Upload to R2
    const customUrl = await uploadImageToR2(blogPost.imageUrl, filename);
    if (!customUrl) {
      failureCount++;
      continue;
    }
    
    // Update database
    const success = await updateBlogPost(blogPost.slug, customUrl);
    
    if (success) {
      successCount++;
      console.log(`✅ Successfully processed: "${blogPost.slug}"`);
      console.log(`🔗 Image URL: ${customUrl}`);
    } else {
      failureCount++;
    }
    
    // Small delay to avoid overwhelming the services
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`\n📊 Process completed:`);
  console.log(`   ✅ Successful: ${successCount}`);
  console.log(`   ❌ Failed: ${failureCount}`);
  console.log(`   📝 Total: ${BLOG_IMAGES.length}`);
}

// Main execution
if (require.main === module) {
  processAllBlogPosts().catch(console.error);
}

module.exports = { 
  uploadImageToR2, 
  updateBlogPost, 
  processAllBlogPosts 
};
