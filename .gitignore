# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (exclude .env.sample for repository)
.env
.env.local
.env.production
!.env.sample

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Blog management scripts with API keys
blog-creator.js
blog-image-generator.js
temp-images/
BLOG_MANAGEMENT_README.md
