import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { generateStateSlug, generateCitySlug } from '@/lib/utils/seo'

// Helper function to generate attorney slug from title
function generateAttorneySlugFromTitle(title: string): string {
  if (!title) return 'unknown'
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://findcaraccidentattorneys.org'
    
    // Get all attorneys
    const { data: attorneys, error } = await supabase
      .from('findcaraccidentattorneys-clean')
      .select('source_id, title, address, city, state, street, postal_code, country_code, lat, lng, total_score, reviews_count, last_reviews_update')
      .eq('reviews_processed', true)
      .order('total_score', { ascending: false })

    if (error) {
      console.error('Error fetching attorneys for sitemap:', error)
      return new NextResponse('Error generating sitemap', { status: 500 })
    }

    // Get all blog posts
    const { data: blogs, error: blogError } = await supabase
      .from('findcaraccidentattorneys-blog')
      .select('slug, title, published_date, updated_at')
      .lte('published_date', new Date().toISOString())
      .order('published_date', { ascending: false })

    if (blogError) {
      console.error('Error fetching blogs for sitemap:', blogError)
      // Don't fail the whole sitemap for blog errors, just log and continue
    }

    // Get unique states and cities
    const states = [...new Set(attorneys.map(a => a.state))].sort()
    const cities = [...new Set(attorneys.map(a => `${a.city}, ${a.state}`))].sort()

    const currentDate = new Date().toISOString()

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- Homepage -->
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>

  <!-- Search page -->
  <url>
    <loc>${baseUrl}/search</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- Blog main page -->
  <url>
    <loc>${baseUrl}/blog</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
`

    // Add blog posts
    if (blogs && blogs.length > 0) {
      blogs.forEach(blog => {
        const lastmod = blog.updated_at || blog.published_date || currentDate
        sitemap += `  <url>
    <loc>${baseUrl}/blog/${blog.slug}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
`
      })
    }

    // Add state pages
    states.forEach(state => {
      sitemap += `  <url>
    <loc>${baseUrl}/${generateStateSlug(state)}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>
`
    })

    // Add city pages
    cities.forEach(cityState => {
      const [city, state] = cityState.split(', ')
      sitemap += `  <url>
    <loc>${baseUrl}/${generateStateSlug(state)}/${generateCitySlug(city)}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>
`
    })

    // Add attorney profile pages
    attorneys.forEach(attorney => {
      const lastmod = attorney.last_reviews_update || currentDate
      // Generate SEO-friendly slug from attorney title
      const attorneySlug = generateAttorneySlugFromTitle(attorney.title) || 'unknown'
      sitemap += `  <url>
    <loc>${baseUrl}/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${attorneySlug}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>
`
    })

    sitemap += `</urlset>`

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600'
      }
    })
  } catch (error) {
    console.error('Error generating sitemap:', error)
    return new NextResponse('Error generating sitemap', { status: 500 })
  }
}
