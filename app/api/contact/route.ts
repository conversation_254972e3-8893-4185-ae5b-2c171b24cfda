import { NextRequest } from 'next/server'
import { Resend } from 'resend'

const resend = new Resend(process.env.RESEND_API_KEY!)

// Helper function to safely get client info
const getClientSource = (request: NextRequest): string | null => {
  try {
    // Get referrer or origin safely
    const referrer = request.headers.get('referer') || ''
    const origin = request.headers.get('origin') || ''
    const userAgent = request.headers.get('user-agent') || ''

    // Skip if it looks like a dev tool or invalid referrer
    if (referrer.includes('chrome://') ||
        referrer.includes('chrome-extension://') ||
        referrer.includes('devtools://') ||
        referrer === 'com.chrome.devtools.json' ||
        !referrer.trim()) {
      return null
    }

    // Return referrer as source if available
    if (referrer) {
      return referrer
    }

    return null
  } catch (error) {
    console.error('Error getting client source:', error)
    return null
  }
}

export async function POST(request: NextRequest) {
  try {
    const {
      name,
      email,
      phone,
      state,
      message
    }: {
      name: string
      email: string
      phone?: string
      state?: string
      message: string
    } = await request.json()

    // Validate required fields
    if (!name || !email || !message) {
      return Response.json(
        { error: 'Name, email, and message are required fields' },
        { status: 400 }
      )
    }

    const html = `
      <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px;">
        <h1 style="color: #2563eb; margin-bottom: 20px;">New Contact Form Submission</h1>

        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; border-left: 4px solid #2563eb;">
          <p style="margin: 8px 0;"><strong>Name:</strong> ${name}</p>
          <p style="margin: 8px 0;"><strong>Email:</strong> ${email}</p>
          <p style="margin: 8px 0;"><strong>Phone:</strong> ${phone || 'Not provided'}</p>
          <p style="margin: 8px 0;"><strong>State:</strong> ${state || 'Not provided'}</p>
        </div>

        <h2 style="color: #374151; margin: 20px 0 10px 0;">Message:</h2>
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; white-space: pre-wrap;">${message}</div>

        <hr style="margin: 20px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px; margin-top: 20px;">
          This message was sent via the contact form on Find Car Accident Lawyers.
        </p>
      </div>
    `

    const result = await resend.emails.send({
      from: process.env.RESEND_FROM_EMAIL!,
      to: process.env.RESEND_TO_EMAIL!,
      replyTo: email,
      subject: `New Contact Form Submission from ${name}`,
      html: html,
    })

    if (!result.data) {
      throw new Error('Failed to send email via Resend')
    }

    return Response.json({ success: true })

  } catch (error: any) {
    console.error('Contact form error:', error)
    return Response.json(
      { error: error.message || 'Failed to send message. Please try again.' },
      { status: 500 }
    )
  }
}
